import datetime
import json
import uuid

from fastapi import Depends
from sqlalchemy import (
    Boolean,
    Column,
    DateTime,
    Float,
    ForeignKey,
    Integer,
    String,
    Text,
    UniqueConstraint,
    func,
)
from sqlalchemy.dialects.postgresql import JSON, JSONB
from sqlalchemy.orm import relationship as Relationship
from uuid_extensions import uuid7

from app.database.session import Base
from app.schemas.schema import ApprovalStatus, ScheduleContentStatus


class BaseTableModel(Base):
    """This model creates helper methods for all models"""

    __abstract__ = True

    id = Column(String, primary_key=True, index=True, default=lambda: str(uuid7()))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(
        DateTime(timezone=True), server_default=func.now(), onupdate=func.now()
    )

    def to_dict(self):
        """returns a dictionary representation of the instance"""
        obj_dict = self.__dict__.copy()
        del obj_dict["_sa_instance_state"]
        obj_dict["id"] = self.id
        if self.created_at:
            obj_dict["created_at"] = self.created_at.isoformat()
        if self.updated_at:
            obj_dict["updated_at"] = self.updated_at.isoformat()
        return obj_dict

    @classmethod
    def get_all(cls):
        from app.database.session import get_db

        db = Depends(get_db)
        """ returns all instance of the class in the db
        """
        return db.query(cls).all()

    @classmethod
    def get_by_id(cls, id):
        from app.database.session import get_db

        db = Depends(get_db)
        """ returns a single object from the db
        """
        obj = db.query(cls).filter_by(id=id).first()
        return obj


class SocialMediaAccount(BaseTableModel):
    __tablename__ = "social_media_accounts"

    platform = Column(
        String, nullable=False
    )  # e.g., 'facebook', 'instagram', 'twitter'
    organisation_id = Column(String, nullable=False)
    username = Column(String, nullable=False)
    login_status = Column(Boolean, default=False, nullable=False)
    social_media_user_id = Column(String, nullable=True)
    access_token = Column(String, nullable=True)
    access_token_secret = Column(String, nullable=True)
    refresh_token = Column(String, nullable=True)
    expires_at = Column(Integer, nullable=True)
    page_id = Column(String, nullable=True)
    page_access_token = Column(String, nullable=True)

    # Relationships
    posts = Relationship(
        "Post", back_populates="social_media_account", cascade="all, delete-orphan"
    )
    conversations = Relationship(
        "Conversation", back_populates="social_media_account", cascade="all, delete-orphan"
    )
    # if the datas are to remain just clear the main details

    __table_args__ = (
        UniqueConstraint("organisation_id", "platform", name="org_platform_uc"),
    )


class Post(BaseTableModel):
    __tablename__ = "posts"

    social_media_account_id = Column(
        ForeignKey("social_media_accounts.id", ondelete="CASCADE"), nullable=True
    )
    post_id = Column(String, unique=True, nullable=True)

    schedulecontent_id = Column(
        ForeignKey("scheduled_content.id", ondelete="CASCADE"), nullable=True
    )

    # Relationships
    social_media_account = Relationship("SocialMediaAccount", back_populates="posts")
    schedule_content = Relationship(
        "ScheduledContent", back_populates="posts",  cascade="all, delete-orphan", single_parent=True, uselist=False)
    comments = Relationship(
        "Comment", back_populates="post", cascade="all, delete-orphan"
    )
    metrics = Relationship(
        "MediaMetrics", back_populates="post", cascade="all, delete-orphan"
    )


class Comment(BaseTableModel):
    __tablename__ = "comments"

    post_id = Column(ForeignKey("posts.post_id", ondelete="CASCADE"), nullable=True)
    comment_id = Column(String)
    content = Column(Text, nullable=False)
    sender = Column(JSONB, nullable=False)
    parent_id = Column(String, nullable=True)
    media = Column(JSONB, nullable=False)
    created_time = Column(DateTime(timezone=True), nullable=False)
    reactions = Column(JSONB, nullable=True)
    attachments = Column(JSONB, nullable=True)
    extra_data = Column(JSONB, nullable=True)

    post = Relationship("Post", back_populates="comments")

    __table_args__ = (
        UniqueConstraint('post_id', 'comment_id', name='uq_post_comment'),
    )


class ScheduledContent(BaseTableModel):
    __tablename__ = "scheduled_content"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    organization_id = Column(String)
    platforms = Column(JSON, nullable=False)
    post_time = Column(DateTime(timezone=True), nullable=False)
    content = Column(Text, nullable=True, default="")
    user_id = Column(String, nullable=False)
    user_role = Column(String, nullable=False)
    reviewer_ids = Column(JSON, nullable=True)
    media_links = Column(JSON, nullable=True)
    media_type = Column(String, nullable=True)
    is_carousel = Column(Boolean, default=False)
    media_items_info = Column(JSON, nullable=True)
    status = Column(String, default=ScheduleContentStatus.draft)
    approval_status = Column(String, default=ApprovalStatus.pending)
    approved_by = Column(String, nullable=True)
    posts = Relationship(
        "Post", back_populates="schedule_content", cascade="all, delete-orphan"
    )


class Conversation(BaseTableModel):
    __tablename__ = "conversations"

    id = Column(String, index=True, default=lambda: str(uuid7()))
    social_media_account_id = Column(
        ForeignKey("social_media_accounts.id", ondelete="CASCADE"), nullable=False
    )
    updated_time = Column(DateTime(timezone=True), nullable=False)
    convo_id = Column(String, nullable=False, unique=True, primary_key=True, index=True)
    participants = Column(JSONB, nullable=False)
    social_media_account = Relationship("SocialMediaAccount", back_populates="conversations")
    messages = Relationship("Message", back_populates="conversation", cascade="all, delete-orphan")


class Message(BaseTableModel):
    __tablename__ = "messages"

    id = Column(String, index=True, default=lambda: str(uuid7()))
    platform = Column(String, nullable=False)
    sender = Column(JSONB, nullable=False)
    recipient = Column(JSONB, nullable=False)
    message = Column(Text, nullable=True)
    created_time = Column(DateTime(timezone=True), nullable=False)
    conversation_id = Column(
        ForeignKey("conversations.convo_id", ondelete="CASCADE"), nullable=True, index=True
    )
    message_id = Column(String, nullable=False, unique=True, primary_key=True, index=True)
    reactions = Column(JSONB, nullable=True)
    shares = Column(JSONB, nullable=True)
    attachments = Column(JSONB, nullable=True)
    reply_to = Column(JSONB, nullable=True)
    is_echo = Column(Boolean, default=False)
    conversation = Relationship("Conversation", back_populates="messages")


class MediaMetrics(BaseTableModel):
    __tablename__ = "media_metrics"

    scheduled_content_id = Column(
        ForeignKey("scheduled_content.id", ondelete="CASCADE"), nullable=False
    )
    post_id = Column(
        ForeignKey("posts.post_id", ondelete="CASCADE"), nullable=False, index=True
    )
    name = Column(String, nullable=False)
    value = Column(Integer, nullable=False)
    period = Column(String, nullable=False)
    extra_data = Column(JSONB, nullable=True)

    post = Relationship("Post", back_populates="metrics")

    __table_args__ = (
        UniqueConstraint(
            "post_id", "scheduled_content_id", "name", "period",
            name="uq_post_sched_name_period_value"
        ),
    )


class OrganizationCompetitor(Base):
    __tablename__ = "organization_competitors"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    organization_id = Column(String, nullable=False, index=True)
    competitor_id = Column(String, ForeignKey("twitter_competitors.id", ondelete="CASCADE"), nullable=False)
    added_at = Column(DateTime(timezone=True), server_default=func.now())
    notes = Column(String, nullable=True)

    competitor = Relationship("TwitterCompetitor", back_populates="organizations")

    __table_args__ = (
        UniqueConstraint("organization_id", "competitor_id", name="org_competitor_uc"),
    )


class TwitterCompetitor(Base):
    __tablename__ = "twitter_competitors"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    username = Column(String, unique=True, index=True)
    added_at = Column(DateTime(timezone=True), server_default=func.now())
    last_metrics_update = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    metrics = Relationship("TwitterCompetitorMetrics", back_populates="competitor", cascade="all, delete-orphan")
    organizations = Relationship("OrganizationCompetitor", back_populates="competitor", cascade="all, delete-orphan")


class TwitterCompetitorMetrics(Base):
    __tablename__ = "twitter_competitor_metrics"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    competitor_id = Column(String, ForeignKey("twitter_competitors.id"))
    collected_at = Column(DateTime(timezone=True), server_default=func.now())

    # Profile metrics
    followers_count = Column(Integer)
    following_count = Column(Integer)
    tweet_count = Column(Integer)
    listed_count = Column(Integer)

    # Engagement metrics
    avg_likes = Column(Float)
    avg_retweets = Column(Float)
    avg_replies = Column(Float)
    avg_quotes = Column(Float)  # Added for v2 API
    avg_impressions = Column(Float, nullable=True)  # Only available with elevated access
    engagement_rate = Column(Float)

    # Content metrics
    avg_tweet_length = Column(Float)
    media_percentage = Column(Float)
    url_percentage = Column(Float)
    hashtag_percentage = Column(Float)

    competitor = Relationship("TwitterCompetitor", back_populates="metrics")


class FacebookPageMetrics(BaseTableModel):
    __tablename__ = "facebook_page_metrics"

    organisation_id = Column(String, nullable=False, index=True)
    page_id = Column(String, nullable=False)
    collected_at = Column(DateTime(timezone=True), server_default=func.now())

    # Using more descriptive metric names
    total_followers = Column(Integer)  # Total followers (previously page_follows)
    total_impressions = Column(Integer)  # Total impressions (previously page_impressions)
    total_reach = Column(Integer)  # Total reach (previously page_impressions_unique)
    total_engagements = Column(Integer)  # Total engagements (previously page_post_engagements)
    engagement_rate = Column(Float, nullable=True)  # Engagement rate (calculated)

    # Additional columns that might be in the database
    page_video_views = Column(Integer, nullable=True)
    post_clicks = Column(Integer, nullable=True)

    __table_args__ = (
        UniqueConstraint("organisation_id", "collected_at", name="org_fb_metrics_time_uc"),
    )

    # Property methods for backward compatibility
    @property
    def page_follows(self):
        return self.total_followers

    @property
    def page_impressions(self):
        return self.total_impressions

    @property
    def page_impressions_unique(self):
        return self.total_reach

    @property
    def page_post_engagements(self):
        return self.total_engagements


class FacebookAudienceDemographics(BaseTableModel):
    __tablename__ = "facebook_audience_demographics"

    organisation_id = Column(String, nullable=False, index=True)
    page_id = Column(String, nullable=False)
    collected_at = Column(DateTime(timezone=True), server_default=func.now())

    # Store demographic data as JSON
    top_countries = Column(Text)  # JSON string of country data
    top_cities = Column(Text)  # JSON string of city data
    locales = Column(Text)  # JSON string of locale data

    def set_top_countries(self, countries_dict):
        self.top_countries = json.dumps(countries_dict)

    def get_top_countries(self):
        return json.loads(self.top_countries) if self.top_countries else {}

    def set_top_cities(self, cities_dict):
        self.top_cities = json.dumps(cities_dict)

    def get_top_cities(self):
        return json.loads(self.top_cities) if self.top_cities else {}

    def set_locales(self, locales_dict):
        self.locales = json.dumps(locales_dict)

    def get_locales(self):
        return json.loads(self.locales) if self.locales else {}

    __table_args__ = (
        UniqueConstraint("organisation_id", "collected_at", name="org_fb_demographics_time_uc"),
    )


class FacebookGrowthTrend(BaseTableModel):
    __tablename__ = "facebook_growth_trends"

    organisation_id = Column(String, nullable=False, index=True)
    page_id = Column(String, nullable=False)
    collected_at = Column(DateTime(timezone=True), server_default=func.now())
    trend_type = Column(String, nullable=False)  # 'audience', 'engagement', 'reach', 'click_rate'
    month = Column(String, nullable=False)  # Format: 'YYYY-MM'
    value = Column(Integer)
    growth_percentage = Column(Float, nullable=True)

    __table_args__ = (
        UniqueConstraint("organisation_id", "trend_type", "month", name="org_fb_trend_month_uc"),
    )


class FacebookTopPerformingPost(BaseTableModel):
    __tablename__ = "facebook_top_performing_posts"

    organisation_id = Column(String, nullable=False, index=True)
    page_id = Column(String, nullable=False)
    collected_at = Column(DateTime(timezone=True), server_default=func.now())

    post_id = Column(String, nullable=False)
    message = Column(Text, nullable=True)
    created_time = Column(DateTime(timezone=True), nullable=True)
    permalink_url = Column(String, nullable=True)
    full_picture = Column(String, nullable=True)

    # Metrics
    impressions = Column(Integer, nullable=True)
    reach = Column(Integer, nullable=True)
    likes = Column(Integer, nullable=True)
    comments = Column(Integer, nullable=True)
    clicks = Column(Integer, nullable=True)
    engagement_rate = Column(Float, nullable=True)

    # Store additional data as JSON
    attachments = Column(Text, nullable=True)  # JSON string of attachments data
    insights = Column(Text, nullable=True)  # JSON string of insights data
    images = Column(Text, nullable=True)  # JSON string of images data

    def set_attachments(self, attachments_dict):
        self.attachments = json.dumps(attachments_dict) if attachments_dict else None

    def get_attachments(self):
        return json.loads(self.attachments) if self.attachments else {}

    def set_insights(self, insights_dict):
        self.insights = json.dumps(insights_dict) if insights_dict else None

    def get_insights(self):
        return json.loads(self.insights) if self.insights else {}

    def set_images(self, images_list):
        self.images = json.dumps(images_list) if images_list else None

    def get_images(self):
        return json.loads(self.images) if self.images else []

    __table_args__ = (
        UniqueConstraint("organisation_id", "post_id", "collected_at", name="org_fb_top_post_uc"),
    )


class TwitterMetrics(BaseTableModel):
    __tablename__ = "twitter_metrics"

    organisation_id = Column(String, nullable=False, index=True)
    twitter_user_id = Column(String, nullable=False)
    collected_at = Column(DateTime(timezone=True), server_default=func.now())

    # Overview metrics
    followers_count = Column(Integer)
    engagements = Column(Integer)  # Total engagements (likes + retweets + replies)
    impressions = Column(Integer)  # Total impressions
    reach = Column(Integer)  # Unique impressions

    __table_args__ = (
        UniqueConstraint("organisation_id", "collected_at", name="org_twitter_metrics_time_uc"),
    )


class TwitterGrowthTrend(BaseTableModel):
    __tablename__ = "twitter_growth_trends"

    organisation_id = Column(String, nullable=False, index=True)
    twitter_user_id = Column(String, nullable=False)
    collected_at = Column(DateTime(timezone=True), server_default=func.now())
    trend_type = Column(String, nullable=False)  # 'impressions', 'engagement', 'click_rate'
    month = Column(String, nullable=False)  # Format: 'YYYY-MM'
    value = Column(Integer)
    growth_percentage = Column(Float, nullable=True)

    __table_args__ = (
        UniqueConstraint("organisation_id", "trend_type", "month", name="org_twitter_trend_month_uc"),
    )


class TwitterTopPerformingTweet(BaseTableModel):
    __tablename__ = "twitter_top_performing_tweets"

    organisation_id = Column(String, nullable=False, index=True)
    twitter_user_id = Column(String, nullable=False)
    collected_at = Column(DateTime(timezone=True), server_default=func.now())

    tweet_id = Column(String, nullable=False)
    text = Column(Text, nullable=True)
    tweet_created_at = Column(DateTime(timezone=True), nullable=True)
    permalink_url = Column(String, nullable=True)

    # Metrics
    impressions = Column(Integer, nullable=True)
    likes = Column(Integer, nullable=True)
    retweets = Column(Integer, nullable=True)
    replies = Column(Integer, nullable=True)
    url_clicks = Column(Integer, nullable=True)
    profile_clicks = Column(Integer, nullable=True)
    engagement_rate = Column(Float, nullable=True)

    # Store additional data as JSON
    media = Column(Text, nullable=True)  # JSON string of media data
    metrics = Column(Text, nullable=True)  # JSON string of all metrics data

    def set_media(self, media_dict):
        self.media = json.dumps(media_dict) if media_dict else None

    def get_media(self):
        return json.loads(self.media) if self.media else {}

    def set_metrics(self, metrics_dict):
        self.metrics = json.dumps(metrics_dict) if metrics_dict else None

    def get_metrics(self):
        return json.loads(self.metrics) if self.metrics else {}

    __table_args__ = (
        UniqueConstraint("organisation_id", "tweet_id", "collected_at", name="org_twitter_top_tweet_uc"),
    )


class InstagramAccountMetrics(BaseTableModel):
    __tablename__ = "instagram_account_metrics"

    organisation_id = Column(String, nullable=False, index=True)
    instagram_user_id = Column(String, nullable=False)
    collected_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Overview metrics
    followers_count = Column(Integer)
    follows_count = Column(Integer)
    media_count = Column(Integer)
    reach = Column(Integer, nullable=True)
    engagements = Column(Integer, nullable=True)

    __table_args__ = (
        UniqueConstraint("organisation_id", "collected_at", name="org_instagram_metrics_time_uc"),
    )


class InstagramMediaMetrics(BaseTableModel):
    __tablename__ = "instagram_media_metrics"

    organisation_id = Column(String, nullable=False, index=True)
    instagram_user_id = Column(String, nullable=False)
    collected_at = Column(DateTime(timezone=True), server_default=func.now())

    media_id = Column(String, nullable=False)
    media_type = Column(String, nullable=True)
    caption = Column(Text, nullable=True)
    permalink = Column(String, nullable=True)
    timestamp = Column(String, nullable=True)

    # Metrics
    total_interactions = Column(Integer, nullable=True)
    views = Column(Integer, nullable=True)
    reach = Column(Integer, nullable=True)

    __table_args__ = (
        UniqueConstraint("organisation_id", "media_id", "collected_at", name="org_instagram_media_metrics_uc"),
    )


class InstagramGrowthTrend(BaseTableModel):
    __tablename__ = "instagram_growth_trends"

    organisation_id = Column(String, nullable=False, index=True)
    instagram_user_id = Column(String, nullable=False)
    collected_at = Column(DateTime(timezone=True), server_default=func.now())
    trend_type = Column(String, nullable=False)  # 'followers', 'engagement', 'reach'
    month = Column(String, nullable=False)  # Format: 'YYYY-MM'
    value = Column(Integer)
    growth_percentage = Column(Float, nullable=True)

    __table_args__ = (
        UniqueConstraint("organisation_id", "trend_type", "month", name="org_instagram_trend_month_uc"),
    )


class FacebookAudienceMetricTimeSeries(BaseTableModel):
    __tablename__ = "facebook_audience_metric_timeseries"

    organisation_id = Column(String, nullable=False, index=True)
    page_id = Column(String, nullable=False, index=True)
    metric_name = Column(String, nullable=False)
    period = Column(String, nullable=False)
    values = Column(JSONB, nullable=False)  # List of {"value": int, "end_time": str}
    title = Column(String, nullable=True)
    description = Column(Text, nullable=True)
    collected_at = Column(DateTime(timezone=True), server_default=func.now())

    __table_args__ = (
        UniqueConstraint(
            "organisation_id", "page_id", "metric_name", "period",
            name="org_fb_metric_timeseries_uc"
        ),
    )


class InstagramAudienceDemographics():
    __tablename__ = "instagram_audience_demographics"

    organisation_id = Column(String, nullable=False, index=True)
    instagram_user_id = Column(String, nullable=False)
    collected_at = Column(DateTime(timezone=True), server_default=func.now())
    metric_name = Column(String, nullable=False)  # e.g., engaged_audience_demographics
    period = Column(String, nullable=False)  # e.g., lifetime
    timeframe = Column(String, nullable=False)  # e.g., last_90_days
    breakdown_type = Column(String, nullable=False)  # country, city, gender, age
    breakdown_value = Column(String, nullable=False)  # e.g., US, female, 18-24, New York
    value = Column(Integer, nullable=False)

    # Optionally store the full breakdowns as JSON for flexibility
    extra_data = Column(JSONB, nullable=True)

    __table_args__ = (
        UniqueConstraint(
            "organisation_id",
            "instagram_user_id",
            "metric_name",
            "period",
            "timeframe",
            "breakdown_type",
            "breakdown_value",
            "collected_at",
            name="org_ig_demographics_uc"
        ),
    )

# ... [rest of the file remains unchanged] ...
