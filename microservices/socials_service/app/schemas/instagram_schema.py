from datetime import datetime, timedelta, timezone
from enum import Enum
from typing import Any, Dict, List, Literal, Optional, Union

from pydantic import (
    BaseModel,
    Field,
    HttpUrl,
    field_validator,
    model_validator,
    root_validator,
)
from typing import Optional, Dict, Any


class MediaType(str, Enum):
    """Enum for Instagram media message types"""
    AUDIO = "audio"
    VIDEO = "video"
    IMAGE = "image"


class InstagramCreateMediaType(str, Enum):
    """Enum for Instagram create media types"""
    IMAGE = "image"
    VIDEO = "video"
    CAROUSEL = "carousel"
    STORIES = "stories"
    REELS = "reels"

    @classmethod
    def get_available_values(cls) -> List[str]:
        """Get list of available media type values"""
        return [e.value for e in cls]

    @classmethod
    def get_available_options(cls) -> str:
        """Get formatted string of available media type options for UI"""
        return f"Available options: {', '.join(cls.get_available_values())}"


class InstagramMediaObjectType(str, Enum):
    """Enum for Instagram media object types from API response"""
    IMAGE = "IMAGE"
    VIDEO = "VIDEO"
    CAROUSEL_ALBUM = "CAROUSEL_ALBUM"
    FEED = "FEED"

    @classmethod
    def get_available_values(cls) -> List[str]:
        """Get list of available media type values"""
        return [e.value for e in cls]

    @classmethod
    def get_available_options(cls) -> str:
        """Get formatted string of available media type options for UI"""
        return f"Available options: {', '.join(cls.get_available_values())}"


class InitialiseInstagram(BaseModel):
    social_media_user_id: str
    access_token: str
    username: str
    platform_name: str


class Recipient(BaseModel):
    id: str


class InstagramTextMessage(BaseModel):
    recipient: Recipient
    text: str


class InstagramMediaMessage(BaseModel):
    recipient: Recipient
    media_type: MediaType
    url: HttpUrl

    @field_validator("media_type", mode="before")
    def confirm_media_type(cls, media_type):
        if isinstance(media_type, str):
            try:
                return MediaType(media_type)
            except ValueError:
                valid_types = [e.value for e in MediaType]
                raise ValueError(
                    f"Invalid media type: {media_type}. "
                    f"Please use one of {', '.join(valid_types)}"
                )
        return media_type

    @field_validator("url", mode="after")
    def validate_url(cls, url):
        if isinstance(url, HttpUrl):
            return str(url)


class InstagramReactionMessage(BaseModel):
    recipient: Recipient
    sender_action: str
    message_id: str
    reaction: Optional[str] = None

    @field_validator("sender_action", mode="before")
    def validate_sender_action(cls, sender_action):
        sender_actions = ["react", "unreact"]
        if sender_action not in sender_actions:
            valid_actions = ', '.join(sender_actions)
            raise ValueError(
                f"Invalid sender actions: {sender_action}. "
                f"Please use one of {valid_actions}"
            )
        return sender_action


class InstagramPostMessage(BaseModel):
    recipient: Recipient
    post_id: str


class InstagramStickerResponse(BaseModel):
    recipient: Recipient
    sticker_type: str = "like_heart"


class InstagramMessageResponse(BaseModel):
    recipient: str
    message_id: str
    message: str
    sent_time: datetime


class InstagramCreate(BaseModel):
    image_url: Optional[HttpUrl] = None
    video_url: Optional[HttpUrl] = None
    media_type: InstagramCreateMediaType
    is_carousel_item: bool = False

    @model_validator(mode='after')
    def validate_upload_path(self):
        if not any([self.image_url, self.video_url]):
            raise ValueError(
                "At least one of image_url, video_url is required."
            )
        if all([self.image_url, self.video_url]):
            raise ValueError(
                "Only one of image_url or video_url should be provided"
            )
        return self

    @field_validator("media_type", mode="before")
    @classmethod
    def confirm_media_type(cls, media_type):
        if isinstance(media_type, str):
            try:
                return InstagramCreateMediaType(media_type)
            except ValueError:
                valid_types = [e.value for e in InstagramCreateMediaType]
                raise ValueError(
                    f"Invalid media type: {media_type}. "
                    f"Please use one of {', '.join(valid_types)}"
                )
        return media_type


class InstagramCarouselCreate(BaseModel):
    medias: List[InstagramCreate]
    caption: Optional[str] = None

    @field_validator("medias", mode="before")
    def validate_medias(cls, medias):
        if not medias:
            raise ValueError("At least one media is required.")
        elif len(medias) > 10:
            raise ValueError("Only ten (10) items can be in a carousel.")
        return medias


# Response Schemas for Instagram Post Operations
class PostStatus(str, Enum):
    """Enum for Instagram post status"""
    PUBLISHED = "published"
    IN_PROGRESS = "in_progress"
    ERROR = "error"
    FINISHED = "finished"


class MediaInfo(BaseModel):
    """Information about a media item in a post"""
    media_type: InstagramCreateMediaType
    url: Optional[HttpUrl] = None
    container_id: Optional[str] = None

    @classmethod
    def get_available_media_types(cls) -> List[str]:
        """Get list of available media type values"""
        return InstagramCreateMediaType.get_available_values()

    @classmethod
    def get_media_type_options(cls) -> str:
        """Get formatted string of available media type options"""
        return InstagramCreateMediaType.get_available_options()


class InstagramSinglePostResponse(BaseModel):
    """Response schema for single Instagram post creation"""
    success: bool = True
    message: str = "Instagram post created successfully"
    container_id: str
    media_id: Optional[str] = None
    media_info: MediaInfo
    status: PostStatus = PostStatus.PUBLISHED
    created_at: Optional[str] = None

    class Config:
        json_schema_extra = {
            "example": {
                "success": True,
                "message": "Instagram post created successfully",
                "container_id": "17841234567890123",
                "media_id": "17851234567890124",
                "media_info": {
                    "media_type": "image",
                    "url": "https://example.com/image.jpg",
                    "container_id": "17841234567890123"
                },
                "status": "published",
                "created_at": "2024-01-15T10:30:00Z"
            }
        }


class CarouselMediaInfo(BaseModel):
    """Information about media items in a carousel"""
    total_items: int
    media_items: List[MediaInfo]

    @field_validator("total_items", mode="before")
    @classmethod
    def validate_total_items(cls, total_items):
        if total_items < 1:
            raise ValueError("Carousel must have at least 1 media item")
        if total_items > 10:
            raise ValueError("Carousel can have maximum 10 media items")
        return total_items


class InstagramCarouselPostResponse(BaseModel):
    """Response schema for Instagram carousel post creation"""
    success: bool = True
    message: str = "Instagram carousel created successfully"
    container_id: str
    media_id: Optional[str] = None
    carousel_info: CarouselMediaInfo
    caption: Optional[str] = None
    status: PostStatus = PostStatus.PUBLISHED
    created_at: Optional[str] = None

    class Config:
        json_schema_extra = {
            "example": {
                "success": True,
                "message": "Instagram carousel created successfully",
                "container_id": "17841234567890125",
                "media_id": "17851234567890126",
                "carousel_info": {
                    "total_items": 3,
                    "media_items": [
                        {
                            "media_type": "image",
                            "url": "https://example.com/image1.jpg",
                            "container_id": "17841234567890127"
                        },
                        {
                            "media_type": "image",
                            "url": "https://example.com/image2.jpg",
                            "container_id": "17841234567890128"
                        },
                        {
                            "media_type": "video",
                            "url": "https://example.com/video1.mp4",
                            "container_id": "17841234567890129"
                        }
                    ]
                },
                "caption": "Check out this amazing carousel post!",
                "status": "published",
                "created_at": "2024-01-15T10:35:00Z"
            }
        }


# Error Response Schemas

class InstagramPostError(BaseModel):
    """Error response schema for Instagram post operations"""
    success: bool = False
    error: str
    error_code: Optional[str] = None
    details: Optional[str] = None

    class Config:
        json_schema_extra = {
            "example": {
                "success": False,
                "error": "Failed to create Instagram post",
                "error_code": "MEDIA_UPLOAD_ERROR",
                "details": "The provided image URL is not accessible"
            }
        }


# Publishing Status Response Schema

class PublishingStatusResponse(BaseModel):
    """Response schema for checking publishing status"""
    container_id: str
    status_code: Optional[str] = None
    status_message: Optional[str] = None

    class Config:
        json_schema_extra = {
            "example": {
                "container_id": "17841234567890123",
                "status_code": "PUBLISHED",
                "status_message": "Media has been successfully published"
            }
        }


# Media Objects Response Schemas

class MediaOwner(BaseModel):
    """Owner information for a media object"""
    id: str


class MediaObject(BaseModel):
    """Individual media object from Instagram API"""
    comments_count: int
    id: str
    like_count: int
    media_type: InstagramMediaObjectType
    media_url: HttpUrl
    owner: MediaOwner
    permalink: HttpUrl
    timestamp: str
    username: str
    thumbnail_url: Optional[HttpUrl] = None
    alt_text: Optional[str] = None
    caption: Optional[str] = None

    @field_validator("media_type", mode="before")
    @classmethod
    def validate_media_type(cls, media_type):
        if isinstance(media_type, str):
            try:
                return InstagramMediaObjectType(media_type)
            except ValueError:
                valid_types = [e.value for e in InstagramMediaObjectType]
                raise ValueError(
                    f"Invalid media type: {media_type}. "
                    f"Please use one of {', '.join(valid_types)}"
                )
        return media_type

    class Config:
        from_attributes = True
        json_schema_extra = {
            "example": {
                "comments_count": 2,
                "id": "18082306612685122",
                "like_count": 5,
                "media_type": "IMAGE",
                "media_url": "https://scontent.cdninstagram.com/v/example.jpg",
                "owner": {
                    "id": "9292384714148098"
                },
                "permalink": "https://www.instagram.com/p/DKKcRNov5Z5/",
                "timestamp": "2025-05-27T16:08:08+0000",
                "username": "example_user",
                "thumbnail_url": "https://example.com/thumb.jpg"
            }
        }


# Comments Response Schemas
class InstagramCommentCreate(BaseModel):
    message: str


class CommentResponse(BaseModel):
    comment_id: str = Field('', alias='id')


class CommentAuthor(BaseModel):
    """Author information for a comment"""
    id: str
    username: str


class CommentMedia(BaseModel):
    """Media information attached to a comment"""
    id: str
    media_type: InstagramMediaObjectType = Field(..., alias='media_type')
    media_url: Optional[str] = None
    thumbnail_url: Optional[str] = None
    alt_text: Optional[str] = None
    caption: Optional[str] = None

    media_product_type: Optional[str] = None

    @root_validator(pre=True)
    def unify_media_type(cls, values):
        if "media_type" not in values and "media_product_type" in values:
            values["media_type"] = values["media_product_type"]
        return values

    @field_validator("media_type", mode="before")
    @classmethod
    def validate_media_type(cls, media_type):
        if isinstance(media_type, str):
            try:
                return InstagramMediaObjectType(media_type)
            except ValueError:
                valid_types = [e.value for e in InstagramMediaObjectType]
                raise ValueError(
                    f"Invalid media type: {media_type}. "
                    f"Please use one of {', '.join(valid_types)}"
                )
        return media_type

    class Config:
        json_schema_extra = {
            "example": {
                "id": "18082306612685122",
                "media_type": "IMAGE",
                "media_url": (
                    "https://scontent.cdninstagram.com/v/t51.75761-15/"
                    "501025553_18005811728769568_5802149702702311328_n.jpg"
                ),
                "thumbnail_url": "https://example.com/thumb.jpg",
                "alt_text": "A beautiful sunset",
                "caption": "Amazing view from the beach"
            }
        }


class InstagramComment(BaseModel):
    """Individual comment from Instagram API"""
    comment_id: str
    content: str
    created_time: datetime
    parent_id: Optional[str] = None
    sender: CommentAuthor
    media: Optional[CommentMedia] = None
    extra_data: Optional[Dict] = None

    class Config:
        populate_by_name = True
        from_attributes = True
        json_schema_extra = {
            "example": {
                "comment_id": "18067688915055635",
                "content": "superb",
                "created_time": "2025-05-27T17:33:45+0000",
                "sender": {
                    "id": "17841453087020874",
                    "username": "aahbeedeen"
                },
                "media": {
                    "id": "18082306612685122",
                    "media_type": "IMAGE",
                    "media_url": (
                        "https://scontent.cdninstagram.com/v/t51.75761-15/"
                        "501025553_18005811728769568_5802149702702311328_n.jpg"
                    )
                },
                "parent_id": None,
                "extra_data": {
                    "like_count": 0,
                    "replies": [
                        {
                            "text": "yes, it's superb",
                            "timestamp": "2025-05-27T17:50:47+0000",
                            "id": "17926464957061739"
                        }
                    ],
                    "hidden": False,
                }
            }
        }


class InstagramCommentResponse(BaseModel):
    comments: List[InstagramComment]
    total: int
    limit: int
    offset: int


class CommentReply(BaseModel):
    """Response schema for get_comments endpoint"""
    comment_id: str
    message: str


class ConversationUser(BaseModel):
    """User information in conversation messages"""
    username: Optional[str] = ''
    id: str
    name: Optional[str] = ''

    @root_validator(pre=True)
    def resolve_username(cls, values):
        values["username"] = values.get("username") or values.get("name", "")
        return values


class ConversationResponse(BaseModel):
    participants: List[ConversationUser]
    updated_time: datetime
    id: str = Field(..., alias="convo_id")


class ConversationRecipientData(BaseModel):
    """Recipient data structure in conversation messages"""
    data: List[ConversationUser]


class StoryReply(BaseModel):
    """Story reply information"""
    link: str
    id: str


class ImageData(BaseModel):
    url: Optional[str]
    preview_url: Optional[str]


class Payload(BaseModel):
    url: Optional[str]


class AttachmentItem(BaseModel):
    image_data: Optional[ImageData] = None
    payload: Optional[Payload] = None

    @property
    def url(self) -> Optional[str]:
        if self.image_data:
            return self.image_data.preview_url or self.image_data.url
        if self.payload:
            return self.payload.url
        return None


class Reactions(BaseModel):
    """Reactions in conversation messages"""
    emoji: Any
    users: List[ConversationUser]


class MessageShares(BaseModel):
    """Shares in conversation messages"""
    link: str


class ConversationMessage(BaseModel):
    """Individual message in a conversation"""
    id: str = Field('...', alias='message_id')
    platform: str
    created_time: datetime
    sender: ConversationUser
    recipient: ConversationUser  # use for single chat
    message: Optional[str]
    conversation_id: str
    attachments: Optional[List[AttachmentItem]] = None
    reactions: Optional[List[Reactions]] = None
    story: Optional[StoryReply] = None
    shares: Optional[List[MessageShares]] = None

    class Config:
        populate_by_name = True


# Type alias for the response which is a list of conversation messages
GetConversationWithUserResponse = List[ConversationMessage]


class InsightsRequestBody(BaseModel):
    period: Optional[str] = "day"
    metric_type: Optional[str] = 'time_series'
    since: Optional[datetime] = datetime.now(timezone.utc) - timedelta(days=30)
    until: Optional[datetime] = datetime.now(timezone.utc)
    timeframe: Optional[datetime] = datetime.now(timezone.utc)

    @model_validator(mode="after")
    def validate_request_params(self):
        valid_period = ["day", "lifetime"]
        valid_metrics = ["total_value", "time_series"]
        # valid_breakdown = ["contact_button_type", "follow_type", "media_product_type"]
        if self.period and self.period not in valid_period:
            raise ValueError(f'period can only be {", ".join(valid_period)}')
        if self.metric_type and self.metric_type not in valid_metrics:
            raise ValueError(f'invalid metric type use one of {", ".join(valid_metrics)}')


class DemographicsRequestBody(BaseModel):
    period: Optional[str] = "day"
    timeframe: Optional[str] = "this_month"


class DemographicDataItem(BaseModel):
    # Example: { "country": "US", "value": 7 } or { "gender": "male", "age": "25-34", "value": 10 }
    value: int
    country: Optional[str] = None
    city: Optional[str] = None
    age: Optional[str] = None
    gender: Optional[Literal["male", "female"]] = None


class MetricBreakdown(BaseModel):
    metric: str  # e.g., engaged_audience_demographics
    period: str  # e.g., lifetime
    timeframe: Optional[str] = None  # e.g., LAST_90_DAYS
    data: List[DemographicDataItem]


class AccountInfo(BaseModel):
    username: str
    account_id: str
    period: str


class DemographicsResponseData(BaseModel):
    account_info: AccountInfo
    metrics: List[MetricBreakdown]


class DemographicsResponse(BaseModel):
    status_code: int = Field(200)
    message: str = Field(..., example="Account insights retrieved successfully")
    data: DemographicsResponseData

class InstagramAudienceGrowthTrendPoint(BaseModel):
    collected_at: datetime
    followers_count: int

    class Config:
        from_attributes = True
class InstagramPostEngagementTrendPoint(BaseModel):
    collected_at: datetime
    engagement: int

    class Config:
        from_attributes = True

class InstagramEngagementTrendPoint(BaseModel):
    collected_at: datetime
    engagements: int

    class Config:
        from_attributes = True


class InstagramAccountReachTrendPoint(BaseModel):
    collected_at: datetime
    reach: int

    class Config:
        from_attributes = True

class InstagramTopPerformingPostSchema(BaseModel):
    media_id: str
    media_type: str = None
    caption: str = None
    permalink: str = None
    timestamp: str = None
    total_interactions: int = None
    views: int = None
    reach: int = None
    collected_at: datetime

    class Config:
        from_attributes = True

# Database serialization schema for InstagramAudienceDemographics
class InstagramAudienceDemographicsSchema(BaseModel):
    id: str
    organisation_id: str
    instagram_user_id: str
    collected_at: datetime
    metric_name: str
    period: str
    timeframe: str
    breakdown_type: str
    breakdown_value: str
    value: int
    extra_data: Optional[Dict[str, Any]] = None

    class Config:
        from_attributes = True
class InstagramOverviewMetricsSchema(BaseModel):
    followers_count: int
    reach: int
    engagements: int
    follows_count: int
    media_count: int
    collected_at: datetime

    class Config:
        from_attributes = True

# reach metrics
class MediaProductType(str, Enum):
    CAROUSEL_CONTAINER = "CAROUSEL_CONTAINER"
    POST = "POST"
    REELS = "REELS"
    STORY = "STORY"
    # add any other known media product types here


class ReachDataItem(BaseModel):
    media_product_type: MediaProductType = Field(..., description="Type of Instagram media")
    value: int = Field(..., ge=0, description="Reach value for this media type")


class MetricReach(BaseModel):
    metric: str = Field("reach", description="Metric name")
    period: str = Field(..., description="Period over which this was measured (e.g. day)")
    total: int = Field(..., ge=0, description="Overall reach total")
    data: List[ReachDataItem] = Field(
        ..., description="Breakdown of reach by media_product_type"
    )


class Paging(BaseModel):
    previous: Optional[str] = Field(None, description="URL to previous page of results")
    next: Optional[str] = Field(None, description="URL to next page of results")


class ReachResponseData(BaseModel):
    metric: MetricReach
    paging: Paging


class ReachResponse(BaseModel):
    status_code: int = Field(200, description="HTTP status code")
    message: str = Field(..., example="Reach metrics retrieved successfully")
    data: ReachResponseData


# media metrics
class MetricValue(BaseModel):
    value: Union[int, float, dict, str]


class ScalarMetric(BaseModel):
    """
    A metric that does not support breakdowns, e.g. 'views', 'likes', etc.
    """
    scheduled_content_id: str
    name: str = Field(..., description="Metric name, e.g. 'views'")
    period: Optional[str] = Field(None, description="Reporting period, e.g. 'lifetime'")
    value: int

    class Config:
        json_schema_extra = {
            "example": {
                "scheduled_content_id": "067f5b49-f89c-762e-8030-4c56cv99a3de",
                "name": "views",
                "period": "lifetime",
                "values": 19
            }
        }


class BreakdownMetric(BaseModel):
    """
    A metric that supports breakdowns, e.g. 'profile_activity' → action_type.
    """
    metric: str = Field(..., description="Metric name, e.g. 'profile_activity'")
    period: Optional[str] = Field(None, description="Reporting period, e.g. 'lifetime'")
    total: int = Field(..., ge=0, description="The total value across all breakdowns")
    data: List[Dict[str, Any]] = Field(
        ...,
        description=(
            "List of breakdown entries; each dict has one or more "
            "keys (e.g. 'action_type') plus the 'value' field"
        )
    )


MetricItem = Union[ScalarMetric, BreakdownMetric]


class MediaInsightsData(BaseModel):
    media_id: str = Field(..., description="Instagram media object ID")
    metrics: List[MetricItem] = Field(
        ..., description="List of metrics; some may have breakdowns"
    )

    class Config:
        json_schema_extra = {
            "example": {
                "media_id": "17932174733377207",
                "metrics": [
                    {
                        "metric": "views",
                        "period": "lifetime",
                        "value": 1234
                    },
                    {
                        "metric": "profile_activity",
                        "period": "lifetime",
                        "total": 4,
                        "data": [
                            {"action_type": "email", "value": 1},
                            {"action_type": "text", "value": 1},
                            {"action_type": "direction", "value": 1},
                            {"action_type": "bio_link_clicked", "value": 1}
                        ]
                    }
                ]
            }
        }


class MediaInsightsResponse(BaseModel):
    """
    NOTE: Don’t request both breakdown-supporting and non-breakdown metrics
    in the same query, or the API may return “An unknown error has occurred.”
    """
    status_code: int = Field(..., description="HTTP status code, e.g. 200")
    message: str = Field(..., example="Media insights retrieved successfully")
    data: MediaInsightsData
