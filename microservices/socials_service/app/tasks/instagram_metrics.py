import asyncio
import json
import traceback
from datetime import datetime, timedelta, timezone
from dateutil.relativedelta import relativedelta
# from app.services.instagram import (
#     fetch_conversations_and_messages, save_comments_to_db)
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from sqlalchemy.dialects.postgresql import insert
from sqlalchemy.orm import selectinload
from app.models import model
from app.database.session import SessionLocal
from app.utils.redis_cache import redis_client
from app.utils.logger import get_logger
import httpx
from redis.exceptions import RedisError
from app.core.config import settings
logger = get_logger(__name__)

# Constants
INSTAGRAM_API_BASE_URL = settings.INSTAGRAM_API_BASE_URL
redis_expiry_time = 60 * 60 * 12  # 12 hours
timeout = httpx.Timeout(10.0, connect=10.0, read=15.0, write=20.0)

# Error classes for specific handling
class InstagramAPIError(Exception):
    """Exception raised for Instagram API errors"""
    def __init__(self, status_code, message, response_data=None):
        self.status_code = status_code
        self.message = message
        self.response_data = response_data
        super().__init__(f"Instagram API Error ({status_code}): {message}")

class InstagramRateLimitError(InstagramAPIError):
    """Exception raised for Instagram API rate limit errors"""
    def __init__(self, message, retry_after=None):
        super().__init__(429, message)
        self.retry_after = retry_after

class InstagramAuthError(InstagramAPIError):
    """Exception raised for Instagram API authentication errors"""
    def __init__(self, message):
        super().__init__(401, message)

class InstagramDataError(Exception):
    """Exception raised for errors in processing Instagram data"""
    pass

class InstagramCacheError(Exception):
    """Exception raised for errors in caching Instagram data"""
    pass


async def fetch_and_store_instagram_metrics():
    """
    Fetch all Instagram metrics for all organizations and store them in the database and Redis.
    This function is meant to be called by a scheduler twice a day.
    """
    logger.info("Starting scheduled Instagram metrics update")

    # Track overall statistics
    total_accounts = 0
    successful_accounts = 0
    failed_accounts = 0
    rate_limited_accounts = 0

    async with SessionLocal() as db:
        try:
            # Get all Instagram social media accounts
            try:
                result = await db.execute(
                    select(model.SocialMediaAccount)
                    .options(selectinload(model.SocialMediaAccount.posts))
                    .where(
                        model.SocialMediaAccount.platform == "instagram"
                    )
                )
                social_accounts = result.scalars().all()
                total_accounts = len(social_accounts)
            except SQLAlchemyError as e:
                logger.error(f"Database error when fetching Instagram accounts: {str(e)}")
                raise

            logger.info(f"Found {total_accounts} Instagram accounts to update")

            # Process accounts in batches to avoid long transactions
            batch_size = 10
            for i in range(0, total_accounts, batch_size):
                batch = social_accounts[i:i+batch_size]
                logger.info(
                    f"Processing batch {i//batch_size + 1} of "
                    f"{(total_accounts + batch_size - 1)//batch_size} batches")

                for account in batch:
                    # Track success/failure for each account
                    account_success = False
                    rate_limited = False
                    try:
                        # await fetch_conversations_and_messages(account)
                        pass
                    except Exception as e:
                        logger.error(f'error occurred in updating conversations and messages: {str(e)}')

                    try:
                        # for posts in account.posts:
                        #     await save_comments_to_db(
                        #         posts.post_id, account.access_token)
                        pass
                    except Exception as e:
                        logger.error(f'error occurred in updating comments {str(e)}')

                    try:
                        # Fetch and store account metrics (overview)
                        try:
                            await fetch_and_store_account_metrics(account, db)
                        except InstagramRateLimitError as e:
                            logger.warning(f"Rate limit hit when fetching account metrics for {account.username}. Retry after {e.retry_after} seconds")
                            rate_limited = True
                            continue
                        except Exception as e:
                            logger.error(f"Error fetching account metrics for {account.username}: {str(e)}")

                        # Fetch and store audience demographics
                        # try:
                        #     await fetch_and_store_audience_demographics(account, db)
                        # except Exception as e:
                        #     logger.error(f"Error fetching audience demographics for {account.username}: {str(e)}")
                            # Continue with other metrics even if this one fails

                        # Fetch and store media metrics
                        try:
                            # await fetch_and_store_media_metrics(account, db)
                            pass
                        except InstagramRateLimitError as e:
                            logger.warning(f"Rate limit hit when fetching media metrics for {account.username}. Retry after {e.retry_after} seconds")
                            rate_limited = True
                            # Continue with commit even if this step fails due to rate limiting
                        except Exception as e:
                            logger.error(f"Error fetching media metrics for {account.username}: {str(e)}")
                            # Continue with commit even if this step fails

                        # Mark as successful if we got this far
                        account_success = True
                        logger.info(f"Successfully updated metrics for Instagram account: {account.username}")

                    except Exception as e:
                        logger.error(f"Error updating metrics for Instagram account {account.username}: {str(e)}")
                        logger.error(traceback.format_exc())

                    # Update statistics
                    if rate_limited:
                        rate_limited_accounts += 1
                    elif account_success:
                        successful_accounts += 1
                    else:
                        failed_accounts += 1

                    # Sleep to avoid rate limiting
                    await asyncio.sleep(1)

                # Commit after each batch
                try:
                    await db.commit()
                    logger.info(f"Committed batch {i//batch_size + 1}")
                except SQLAlchemyError as e:
                    await db.rollback()
                    logger.error(f"Database error when committing batch {i//batch_size + 1}: {str(e)}")
                    # Continue with next batch even if this one fails

            logger.info(f"Completed scheduled Instagram metrics update. Total: {total_accounts}, Successful: {successful_accounts}, Failed: {failed_accounts}, Rate Limited: {rate_limited_accounts}")

        except Exception as e:
            await db.rollback()
            logger.error(f"Error in scheduled Instagram metrics update: {str(e)}")
            logger.error(traceback.format_exc())


async def fetch_and_store_account_metrics(account, db: AsyncSession):
    """Fetch and store account metrics (overview: followers, reach, engagements)"""
    try:
        # Fetch metrics from Instagram Insights API
        async with httpx.AsyncClient(timeout=timeout) as client:
            metrics = "follower_count,reach,accounts_engaged"
            url = f"{INSTAGRAM_API_BASE_URL}/{account.social_media_user_id}/insights"
            params = {
                "metric": metrics,
                "period": "day",
                "access_token": account.access_token,
            }
            response = await client.get(url, params=params)
            if response.status_code == 401 or response.status_code == 403:
                error_data = response.json()
                error_message = error_data.get('error', {}).get('message', 'Authentication error')
                logger.error(f"Instagram auth error for {account.username}: {error_message}")
                raise InstagramAuthError(error_message)
            elif response.status_code == 429:
                retry_after = response.headers.get('Retry-After', '60')
                logger.warning(f"Rate limit exceeded for {account.username}. Retry after {retry_after} seconds")
                raise InstagramRateLimitError(f"Rate limit exceeded", retry_after=retry_after)
            response.raise_for_status()
            data = response.json().get("data", [])

            # Parse metrics
            followers_count = None
            reach = None
            engagements = None
            for item in data:
                name = item.get("name")
                values = item.get("values", [])
                value = None
                if values:
                    if isinstance(values[0], dict) and "value" in values[0]:
                        value = values[0].get("value", 0)
                    else:
                        value = values[0] if values[0] is not None else 0
                if name == "follower_count":
                    followers_count = value
                elif name == "reach":
                    reach = value
                elif name == "accounts_engaged":
                    engagements = value

            # Fallback to 0 if not found
            followers_count = followers_count if followers_count is not None else 0
            reach = reach if reach is not None else 0
            engagements = engagements if engagements is not None else 0

            # Optionally fetch follows_count and media_count from user profile
            follows_count = 0
            media_count = 0
            try:
                profile_resp = await client.get(
                    f"{INSTAGRAM_API_BASE_URL}/{account.social_media_user_id}",
                    params={
                        "access_token": account.access_token,
                        "fields": "follows_count,media_count,followers_count",
                    },
                )
                if profile_resp.status_code == 200:
                    profile_data = profile_resp.json()
                    follows_count = profile_data.get("follows_count", 0)
                    media_count = profile_data.get("media_count", 0)
                    followers_count = profile_data.get("followers_count", followers_count)
            except Exception as e:
                logger.warning(f"Could not fetch profile fields for {account.username}: {str(e)}")

        # Create metrics record
        metrics = model.InstagramAccountMetrics(
            organisation_id=account.organisation_id,
            instagram_user_id=account.social_media_user_id,
            followers_count=followers_count,
            follows_count=follows_count,
            media_count=media_count,
            reach=reach,
            engagements=engagements,
            collected_at=datetime.now(timezone.utc)
        )

        # Add metrics to database with error handling
        try:
            db.add(metrics)
            await db.commit()
        except SQLAlchemyError as e:
            logger.error(f"Database error for {account.username}: {str(e)}")
            raise

        logger.info(f"Updated account metrics for Instagram account: {account.username}")

    except InstagramAPIError as e:
        logger.error(f"Instagram API error for {account.username}: {e.message}")
        if isinstance(e, InstagramRateLimitError):
            logger.warning(f"Will retry after {e.retry_after} seconds")
        raise
    except InstagramDataError as e:
        logger.error(f"Data processing error for {account.username}: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error fetching account metrics for {account.username}: {str(e)}")
        logger.error(traceback.format_exc())
        raise

async def fetch_and_store_audience_demographics(account, db: AsyncSession):
    """
    Fetch and store Instagram audience demographics for the given account.
    Fetches breakdowns for country, city, gender, and age.
    """
    logger.info(f"Fetching audience demographics for Instagram account: {account.username}")

    breakdown_types = ["country", "city", "gender", "age"]
    metric_name = "engaged_audience_demographics"
    period = "lifetime"
    timeframe = "last_90_days"  # You may adjust this as needed

    async with httpx.AsyncClient(timeout=timeout) as client:
        for breakdown in breakdown_types:
            params = {
                "metric": metric_name,
                "period": period,
                "timeframe": timeframe,
                "breakdowns": breakdown,
                "metric_type": "total_value",
                "access_token": account.access_token,
            }
            url = f"{INSTAGRAM_API_BASE_URL}/{account.social_media_user_id}/insights"
            try:
                response = await client.get(url, params=params)
                if response.status_code == 401 or response.status_code == 403:
                    error_data = response.json()
                    error_message = error_data.get('error', {}).get('message', 'Authentication error')
                    logger.error(f"Instagram auth error for {account.username}: {error_message}")
                    raise InstagramAuthError(error_message)
                elif response.status_code == 429:
                    retry_after = response.headers.get('Retry-After', '60')
                    logger.warning(f"Rate limit exceeded for {account.username}. Retry after {retry_after} seconds")
                    raise InstagramRateLimitError(f"Rate limit exceeded", retry_after=retry_after)
                response.raise_for_status()
                data = response.json().get("data", [])
                if not data:
                    logger.info(f"No demographic data for {account.username} breakdown={breakdown}")
                    continue

                for item in data:
                    total_value = item.get("total_value", {})
                    breakdowns = total_value.get("breakdowns", [])
                    for breakdown_set in breakdowns:
                        dimension_keys = breakdown_set.get("dimension_keys", [])
                        results = breakdown_set.get("results", [])
                        for result in results:
                            dimension_values = result.get("dimension_values", [])
                            value = result.get("value", 0)
                            # Map dimension_keys to values
                            breakdown_dict = dict(zip(dimension_keys, dimension_values))
                            breakdown_value = breakdown_dict.get(breakdown, None)
                            if not breakdown_value:
                                continue
                            stmt = insert(model.InstagramAudienceDemographics).values(
                                organisation_id=account.organisation_id,
                                instagram_user_id=account.social_media_user_id,
                                collected_at=datetime.now(),
                                metric_name=metric_name,
                                period=period,
                                timeframe=timeframe,
                                breakdown_type=breakdown,
                                breakdown_value=breakdown_value,
                                value=value,
                                extra_data=breakdown_dict
                            ).on_conflict_do_update(
                                index_elements=[
                                    "organisation_id",
                                    "instagram_user_id",
                                    "metric_name",
                                    "period",
                                    "timeframe",
                                    "breakdown_type",
                                    "breakdown_value",
                                    "collected_at"
                                ],
                                set_={
                                    "value": value,
                                    "extra_data": breakdown_dict,
                                    "updated_at": datetime.now()
                                }
                            )
                            await db.execute(stmt)
                logger.info(f"Stored {breakdown} demographics for {account.username}")
            except InstagramRateLimitError as e:
                logger.warning(f"Rate limit hit for {account.username} on {breakdown}. Retry after {e.retry_after} seconds")
                continue
            except Exception as e:
                logger.error(f"Error fetching/storing {breakdown} demographics for {account.username}: {str(e)}")
                logger.error(traceback.format_exc())


# #################################
# get media insights
async def fetch_and_store_media_metrics(account, db_session: AsyncSession):
    """
    Fetch and store media metrics for all posts for the given Instagram account.
    Saves metrics to InstagramMediaMetrics for top performing post queries.
    """
    try:
        logger.info("Fetching media insights for all posts")
        result = await db_session.execute(
            select(model.Post)
            .where(
                model.Post.social_media_account_id == account.id
            )
        )
        posts = result.scalars().all()
        metrics_to_fetch = [
            "reach",
            "total_interactions",
            "views",
            "likes",
            "comments",
            "saved",
            "shares",
            "profile_visits"
        ]
        async with httpx.AsyncClient(timeout=timeout) as client:
            for post in posts:
                url = f"{settings.INSTAGRAM_API_BASE_URL}/{post.post_id}/insights"
                params = {
                    "metric": ",".join(metrics_to_fetch),
                    "access_token": account.access_token,
                }
                try:
                    resp = await client.get(url, params=params)
                    resp.raise_for_status()
                    payload = resp.json()
                    data = payload.get("data", [])
                    metrics_dict = {}
                    for item in data:
                        name = item.get("name")
                        values = item.get("values", [])
                        value = None
                        if values:
                            if isinstance(values[0], dict) and "value" in values[0]:
                                value = values[0].get("value", 0)
                            else:
                                value = values[0] if values[0] is not None else 0
                        metrics_dict[name] = value

                    # Save to InstagramMediaMetrics
                    stmt = insert(model.InstagramMediaMetrics).values(
                        organisation_id=account.organisation_id,
                        instagram_user_id=account.social_media_user_id,
                        media_id=post.post_id,
                        media_type=getattr(post, "media_type", None),
                        caption=getattr(post, "caption", None),
                        permalink=getattr(post, "permalink", None),
                        timestamp=getattr(post, "timestamp", None),
                        total_interactions=metrics_dict.get("total_interactions"),
                        views=metrics_dict.get("views"),
                        reach=metrics_dict.get("reach"),
                        collected_at=datetime.now(timezone.utc)
                    ).on_conflict_do_update(
                        index_elements=['organisation_id', 'media_id', 'collected_at'],
                        set_={
                            "media_type": getattr(post, "media_type", None),
                            "caption": getattr(post, "caption", None),
                            "permalink": getattr(post, "permalink", None),
                            "timestamp": getattr(post, "timestamp", None),
                            "total_interactions": metrics_dict.get("total_interactions"),
                            "views": metrics_dict.get("views"),
                            "reach": metrics_dict.get("reach"),
                            "updated_at": datetime.now(timezone.utc)
                        }
                    )
                    await db_session.execute(stmt)
                except Exception as e:
                    logger.error(f"Error fetching/saving media metrics for post {post.post_id}: {str(e)}")
        await db_session.commit()
        logger.info("Media metrics updated for all posts")
    except httpx.RequestError as e:
        logger.error(f"Request error for media insights: {str(e)}")
    except Exception as e:
        logger.error(f"Request error for media insights: {str(e)}")


async def scheduled_instagram_metrics_update():
    """
    Function to be called by the scheduler to update Instagram metrics for all accounts
    """
    await fetch_and_store_instagram_metrics()
